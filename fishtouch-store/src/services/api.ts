import { invoke } from "@tauri-apps/api/core";
import type {
  Package, NewPackage,
  Item, NewItem,
  Recipe, NewRecipe,
  Batch, NewBatch,
  PurchaseOrder, NewPurchaseOrder,
  ShipmentOrder, NewShipmentOrder,
  InventoryTransaction,
  StockLevel,
  LowStockAlert,
  ApiResult
} from "../types";

// 包装管理 API
export const packageApi = {
  async create(data: NewPackage): Promise<Package> {
    const result: ApiResult<Package> = await invoke("create_package", {
      name: data.name,
      description: data.description
    });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async getAll(): Promise<Package[]> {
    const result: ApiResult<Package[]> = await invoke("get_all_packages");
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async getById(id: number): Promise<Package> {
    const result: ApiResult<Package> = await invoke("get_package_by_id", { id });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async update(id: number, data: NewPackage): Promise<Package> {
    const result: ApiResult<Package> = await invoke("update_package", {
      id,
      name: data.name,
      description: data.description
    });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async delete(id: number): Promise<number> {
    const result: ApiResult<number> = await invoke("delete_package", { id });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  }
};

// 单品管理 API
export const itemApi = {
  async create(data: NewItem): Promise<Item> {
    const result: ApiResult<Item> = await invoke("create_item", {
      name: data.name,
      sku: data.sku
    });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async getAll(): Promise<Item[]> {
    const result: ApiResult<Item[]> = await invoke("get_all_items");
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async getById(id: number): Promise<Item> {
    const result: ApiResult<Item> = await invoke("get_item_by_id", { id });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async getBySku(sku: string): Promise<Item> {
    const result: ApiResult<Item> = await invoke("get_item_by_sku", { sku });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async update(id: number, data: Partial<NewItem>): Promise<Item> {
    const result: ApiResult<Item> = await invoke("update_item", {
      id,
      name: data.name,
      sku: data.sku
    });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async delete(id: number): Promise<number> {
    const result: ApiResult<number> = await invoke("delete_item", { id });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  }
};

// 配方管理 API
export const recipeApi = {
  async create(data: NewRecipe): Promise<Recipe> {
    const result: ApiResult<Recipe> = await invoke("create_recipe", {
      package_id: data.package_id,
      item_id: data.item_id,
      quantity: data.quantity,
      unit: data.unit,
      valid_from: data.valid_from,
      valid_to: data.valid_to
    });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async getByPackageId(packageId: number): Promise<Recipe[]> {
    const result: ApiResult<Recipe[]> = await invoke("get_recipes_by_package_id", { package_id: packageId });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async getAll(): Promise<Recipe[]> {
    const result: ApiResult<Recipe[]> = await invoke("get_all_recipes");
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async getById(id: number): Promise<Recipe> {
    const result: ApiResult<Recipe> = await invoke("get_recipe_by_id", { id });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async update(id: number, data: Partial<NewRecipe>): Promise<Recipe> {
    const result: ApiResult<Recipe> = await invoke("update_recipe", {
      id,
      quantity: data.quantity,
      unit: data.unit,
      valid_from: data.valid_from,
      valid_to: data.valid_to
    });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async delete(id: number): Promise<number> {
    const result: ApiResult<number> = await invoke("delete_recipe", { id });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  }
};

// 批次管理 API
export const batchApi = {
  async create(data: NewBatch): Promise<Batch> {
    const result: ApiResult<Batch> = await invoke("create_batch", {
      item_id: data.item_id,
      batch_number: data.batch_number,
      in_date: data.in_date,
      expiry_date: data.expiry_date
    });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async getByItemId(itemId: number): Promise<Batch[]> {
    const result: ApiResult<Batch[]> = await invoke("get_batches_by_item_id", { item_id: itemId });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async getAll(): Promise<Batch[]> {
    const result: ApiResult<Batch[]> = await invoke("get_all_batches");
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async getById(id: number): Promise<Batch> {
    const result: ApiResult<Batch> = await invoke("get_batch_by_id", { id });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async update(id: number, data: Partial<NewBatch>): Promise<Batch> {
    const result: ApiResult<Batch> = await invoke("update_batch", {
      id,
      batch_number: data.batch_number,
      in_date: data.in_date,
      expiry_date: data.expiry_date
    });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async delete(id: number): Promise<number> {
    const result: ApiResult<number> = await invoke("delete_batch", { id });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  }
};

// 采购订单 API
export const purchaseOrderApi = {
  async create(data: NewPurchaseOrder): Promise<PurchaseOrder> {
    const result: ApiResult<PurchaseOrder> = await invoke("create_purchase_order", {
      order_number: data.order_number,
      order_date: data.order_date,
      supplier: data.supplier
    });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  }
};

// 发货订单 API
export const shipmentOrderApi = {
  async create(data: NewShipmentOrder): Promise<ShipmentOrder> {
    const result: ApiResult<ShipmentOrder> = await invoke("create_shipment_order", {
      order_number: data.order_number,
      order_date: data.order_date,
      customer: data.customer
    });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  }
};

// 库存管理 API
export const inventoryApi = {
  async recordPurchaseTransaction(
    batchId: number,
    quantity: number,
    orderNumber: string,
    timestamp: string
  ): Promise<InventoryTransaction> {
    const result: ApiResult<InventoryTransaction> = await invoke("record_purchase_transaction", {
      batch_id: batchId,
      quantity,
      order_number: orderNumber,
      timestamp
    });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async recordShipmentTransaction(
    batchId: number,
    quantity: number,
    orderNumber: string,
    timestamp: string
  ): Promise<InventoryTransaction> {
    const result: ApiResult<InventoryTransaction> = await invoke("record_shipment_transaction", {
      batch_id: batchId,
      quantity,
      order_number: orderNumber,
      timestamp
    });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async getCurrentStockForItem(itemId: number): Promise<StockLevel> {
    const result: ApiResult<StockLevel> = await invoke("get_current_stock_for_item", { item_id: itemId });
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async getAllStockLevels(): Promise<StockLevel[]> {
    const result: ApiResult<StockLevel[]> = await invoke("get_all_stock_levels");
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  },

  async checkLowStockAlerts(): Promise<LowStockAlert[]> {
    const result: ApiResult<LowStockAlert[]> = await invoke("check_low_stock_alerts");
    if (typeof result === "string") {
      throw new Error(result);
    }
    return result;
  }
};
