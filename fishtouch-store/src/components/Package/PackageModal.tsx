import React, { useState, useEffect } from "react";
import {
  <PERSON>alog,
  Button,
  Input,
  Textarea,
  Stack,
  Text,
  Portal,
  CloseButton,
} from "@chakra-ui/react";
import { packageApi } from "../../services/api";
import type { Package, NewPackage } from "../../types";
import { toaster } from "../ui/toaster";

interface PackageModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  package?: Package | null;
  onSuccess: () => void;
}

const PackageModal: React.FC<PackageModalProps> = ({
  open,
  onOpenChange,
  package: editPackage,
  onSuccess,
}) => {
  const [formData, setFormData] = useState<NewPackage>({
    name: "",
    description: "",
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const isEdit = !!editPackage;

  useEffect(() => {
    if (open) {
      if (editPackage) {
        setFormData({
          name: editPackage.name,
          description: editPackage.description || "",
        });
      } else {
        setFormData({
          name: "",
          description: "",
        });
      }
      setErrors({});
    }
  }, [open, editPackage]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = "包装名称不能为空";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      if (isEdit && editPackage) {
        await packageApi.update(editPackage.id, formData);
        toaster.create({
          title: "成功",
          description: "包装更新成功",
          type: "success",
          duration: 3000,
          closable: true,
        });
      } else {
        await packageApi.create(formData);
        toaster.create({
          title: "成功",
          description: "包装创建成功",
          type: "success",
          duration: 3000,
          closable: true,
        });
      }

      onSuccess();
      onOpenChange(false);
    } catch (error) {
      console.error("保存包装失败:", error);
      const errorMessage = error instanceof Error ? error.message : "保存失败，请重试";
      toaster.create({
        title: "错误",
        description: errorMessage,
        type: "error",
        duration: 5000,
        closable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleOpenChange = (details: any) => {
    onOpenChange(details.open);
  };

  return (
    <Dialog.Root open={open} onOpenChange={handleOpenChange}>
      <Portal>
        <Dialog.Backdrop />
        <Dialog.Positioner>
          <Dialog.Content>
            <Dialog.Header>
              <Dialog.Title>{isEdit ? "编辑包装" : "新增包装"}</Dialog.Title>
              <Dialog.CloseTrigger asChild>
                <CloseButton size="sm" />
              </Dialog.CloseTrigger>
            </Dialog.Header>

            <Dialog.Body>
              <Stack gap="4">
                <Stack gap="2">
                  <Text>包装名称 *</Text>
                  <Input
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    placeholder="请输入包装名称"
                  />
                  {errors.name && (
                    <Text color="red.500" fontSize="sm">{errors.name}</Text>
                  )}
                </Stack>

                <Stack gap="2">
                  <Text>描述</Text>
                  <Textarea
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    placeholder="请输入包装描述"
                    rows={3}
                  />
                </Stack>
              </Stack>
            </Dialog.Body>

            <Dialog.Footer>
              <Dialog.ActionTrigger asChild>
                <Button variant="outline">取消</Button>
              </Dialog.ActionTrigger>
              <Button
                colorPalette="blue"
                onClick={handleSubmit}
                loading={loading}
                loadingText={isEdit ? "保存中..." : "创建中..."}
              >
                {isEdit ? "保存" : "创建"}
              </Button>
            </Dialog.Footer>
          </Dialog.Content>
        </Dialog.Positioner>
      </Portal>
    </Dialog.Root>
  );
};

export default PackageModal;
